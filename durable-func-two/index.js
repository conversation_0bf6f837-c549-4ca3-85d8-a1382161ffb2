const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

module.exports = async function (context) {
  const startTime = new Date();
  const input = context.bindings.name || {};
  const responseMessage = "This is a Durable Function 2!!";

  await delay(9000);

  const endTime = new Date();
  const executionTime = endTime - startTime;

  return {
    functionName: "durable-func-two",
    message: responseMessage,
    executionTime: `${executionTime}ms`,
    startTime: startTime.toISOString(),
    endTime: endTime.toISOString(),
    input: input,
  };
};
