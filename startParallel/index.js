const df = require("durable-functions");

module.exports.httpStart = async function (context, req) {
  const client = df.getClient(context);
  const input = req.body || req.query || {};

  const instanceId = await client.startNew("parallel", undefined, input);

  context.log(`Started orchestration with ID = '${instanceId}'.`);

  return client.createCheckStatusResponse(context.bindingData.req, instanceId);
};
