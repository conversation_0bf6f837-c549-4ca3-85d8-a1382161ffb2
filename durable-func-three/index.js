const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

module.exports = async function (context, req) {
  const startTime = new Date();
  const input = req.body || req.query || {};
  const responseMessage = "This is a Durable Function 3!!";

  await delay(4000);

  const endTime = new Date();
  const executionTime = endTime - startTime;

  context.res = {
    status: 200,
    body: {
      functionName: "durable-func-three",
      message: responseMessage,
      executionTime: `${executionTime}ms`,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      input: input,
    },
  };
};
