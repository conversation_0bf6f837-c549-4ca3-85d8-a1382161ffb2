{"name": "azure-functions-app", "version": "1.0.0", "description": "Azure Functions app for blob storage, database operations, and video streaming", "main": "index.js", "scripts": {"start": "func start", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@azure/cosmos": "^4.0.0", "@azure/storage-blob": "^12.17.0", "axios": "^1.11.0", "durable-functions": "^3.2.0", "mime-types": "^2.1.35"}, "keywords": ["azure", "functions", "blob-storage", "database", "video-streaming"], "author": "", "license": "MIT"}